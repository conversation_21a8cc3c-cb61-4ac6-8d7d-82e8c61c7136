import React, { useState, useEffect } from 'react';
import { X, Building2, DollarSign, Lock, Eye, EyeOff, ChevronDown, CreditCard, CheckCircle } from 'lucide-react';
import { getConnectedAccounts, ConnectedBankAccount } from '../../services/plaid';

interface AddMoneyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddMoney: (amount: number, pin: string, bankAccountId?: string) => Promise<{
    success: boolean;
    message?: string;
    transactionId?: number;
  }>;
  currentBalance: number;
  primaryBank: string;
}

const AddMoneyModal: React.FC<AddMoneyModalProps> = ({
  isOpen,
  onClose,
  onAddMoney,
  currentBalance,
  primaryBank
}) => {
  const [amount, setAmount] = useState('');
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPin, setShowPin] = useState(false);
  const [connectedAccounts, setConnectedAccounts] = useState<ConnectedBankAccount[]>([]);
  const [selectedBankAccount, setSelectedBankAccount] = useState<ConnectedBankAccount | null>(null);
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  const [loadingAccounts, setLoadingAccounts] = useState(false);
  const [transactionResult, setTransactionResult] = useState<{
    success: boolean;
    amount: number;
    transactionId?: number;
    message?: string;
  } | null>(null);

  const quickAmounts = [50, 100, 200, 500, 1000];

  // Fetch connected accounts when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchConnectedAccounts();
    }
  }, [isOpen]);

  const fetchConnectedAccounts = async () => {
    try {
      setLoadingAccounts(true);
      const accounts = await getConnectedAccounts();
      setConnectedAccounts(accounts);

      // Set primary account as default selection
      const primaryAccount = accounts.find(account => account.is_primary);
      if (primaryAccount) {
        setSelectedBankAccount(primaryAccount);
      } else if (accounts.length > 0) {
        setSelectedBankAccount(accounts[0]);
      }
    } catch (error) {
      console.error('Error fetching connected accounts:', error);
      setError('Failed to load bank accounts. Please try again.');
    } finally {
      setLoadingAccounts(false);
    }
  };

  const getAccountIcon = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return <Building2 size={16} className="text-blue-600" />;
      if (subtype === 'savings') return <Building2 size={16} className="text-green-600" />;
    }
    return <CreditCard size={16} className="text-gray-600" />;
  };

  const formatAccountType = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return 'Checking';
      if (subtype === 'savings') return 'Savings';
    }
    return 'Account';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const numAmount = parseFloat(amount);
    if (!numAmount || numAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (numAmount < 10) {
      setError('Minimum amount is $10');
      return;
    }

    if (numAmount > 10000) {
      setError('Maximum amount is $10,000');
      return;
    }

    if (!pin || pin.length < 4) {
      setError('Please enter your 4-6 digit wallet PIN');
      return;
    }

    if (!selectedBankAccount) {
      setError('Please select a bank account');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await onAddMoney(numAmount, pin, selectedBankAccount.id);

      if (result.success) {
        // Set transaction result to show success state
        setTransactionResult({
          success: true,
          amount: numAmount,
          transactionId: result.transactionId,
          message: result.message
        });
        setAmount('');
        setPin('');
        setError(null);
      } else {
        setError(result.message || 'Failed to add money');
      }
    } catch {
      setError('Failed to add money. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString());
    setError(null);
  };

  const handleClose = () => {
    // Reset all state when closing
    setAmount('');
    setPin('');
    setError(null);
    setTransactionResult(null);
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full overflow-y-auto">
        {/* Header */}
        <div className="flex items-start justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Add Money to Wallet</h2>

            {/* Current Balance */}
            <div className="flex items-center space-x-2">
              <p className="text-sm text-blue-600 mb-1">Current Balance</p>
              <p className="text-2xl font-bold text-blue-900">${currentBalance.toFixed(2)}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className='max-h-[620px] overflow-y-auto'>
            {/* Success State */}
            {transactionResult?.success ? (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Money Added Successfully!
                  </h3>
                  <p className="text-gray-600">
                    ${transactionResult.amount.toFixed(2)} is being added to your wallet
                  </p>
                  {transactionResult.transactionId && (
                    <p className="text-sm text-gray-500 mt-2">
                      Transaction ID: {transactionResult.transactionId}
                    </p>
                  )}
                  {transactionResult.message && (
                    <p className="text-sm text-blue-600 mt-3 bg-blue-50 p-3 rounded-lg">
                      {transactionResult.message}
                    </p>
                  )}
                </div>

                <button
                  onClick={handleClose}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700"
                >
                  Done
                </button>
              </div>
            ) : (
              <>
            {/* Bank Account Selection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Select Bank Account</h3>
              {loadingAccounts ? (
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <p className="text-sm text-gray-500">Loading bank accounts...</p>
                </div>
              ) : connectedAccounts.length === 0 ? (
                <div className="bg-red-50 rounded-lg p-4 text-center">
                  <p className="text-sm text-red-600">No bank accounts connected. Please connect a bank account first.</p>
                </div>
              ) : (
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowBankDropdown(!showBankDropdown)}
                    className="w-full bg-white border border-gray-300 rounded-lg p-4 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                  >
                    {selectedBankAccount ? (
                      <div className="flex items-center space-x-3">
                        {getAccountIcon(selectedBankAccount.type, selectedBankAccount.subtype)}
                        <div className="text-left">
                          <div className='flex items-center space-x-2'>
                            <p className="font-medium text-gray-900">{selectedBankAccount.name}</p>
                            {selectedBankAccount.is_primary && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 mt-1">
                                Primary
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">
                            {selectedBankAccount.institution_name} • {formatAccountType(selectedBankAccount.type, selectedBankAccount.subtype)} • ••••{selectedBankAccount.mask}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-500">Select a bank account</span>
                    )}
                    <ChevronDown size={20} className={`text-gray-400 transition-transform ${showBankDropdown ? 'rotate-180' : ''}`} />
                  </button>

                  {showBankDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                      {connectedAccounts.map((account) => (
                        <button
                          key={account.id}
                          type="button"
                          onClick={() => {
                            setSelectedBankAccount(account);
                            setShowBankDropdown(false);
                            setError(null);
                          }}
                          className="w-full p-4 text-left hover:bg-gray-50 flex items-center space-x-3 border-b border-gray-100 last:border-b-0"
                        >
                          {getAccountIcon(account.type, account.subtype)}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-gray-900">{account.name}</p>
                              {account.is_primary && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                  Primary
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-500">
                              {account.institution_name} • {formatAccountType(account.type, account.subtype)} • ••••{account.mask}
                            </p>
                            {account.balance.available !== null && (
                              <p className="text-sm text-gray-600 mt-1">
                                Available: ${account.balance.available.toFixed(2)}
                              </p>
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Quick Amount Selection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Select</h3>
              <div className="grid grid-cols-5 gap-2">
                {quickAmounts.map((quickAmount) => (
                  <button
                    key={quickAmount}
                    onClick={() => handleQuickAmount(quickAmount)}
                    className="p-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                  >
                    ${quickAmount}
                  </button>
                ))}
              </div>
            </div>

            {/* Amount Input */}
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                  Enter Amount
                </label>
                <div className="relative">
                  <DollarSign size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="number"
                    id="amount"
                    value={amount}
                    onChange={(e) => {
                      setAmount(e.target.value);
                      setError(null);
                    }}
                    placeholder="0.00"
                    min="10"
                    max="10000"
                    step="0.01"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <p className="text-s text-gray-500 mt-1">Minimum: $10 | Maximum: $10,000</p>
              </div>

              {/* PIN Input */}
              <div className="mb-6">
                <label htmlFor="pin" className="block text-sm font-medium text-gray-700 mb-2">
                  Wallet PIN
                </label>
                <div className="relative">
                  <Lock size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type={showPin ? 'text' : 'password'}
                    id="pin"
                    value={pin}
                    onChange={(e) => {
                      setPin(e.target.value);
                      setError(null);
                    }}
                    placeholder="Enter your wallet PIN"
                    maxLength={6}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500">Enter your 4-6 digit wallet PIN to authorize the transaction</p>
                  <button
                    type="button"
                    onClick={() => window.open('/wallet?tab=settings', '_blank')}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    Forgot PIN?
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleClose}
                  className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading || !amount || !pin}
                  className="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Processing...' : 'Add Money'}
                </button>
              </div>
            </form>

            {/* Security Notice */}
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                🔒 Money will be transferred from your connected primary bank account.
                Your PIN and payment information are secured with bank-level encryption.
                Funds will be available immediately in your wallet.
              </p>
            </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMoneyModal;